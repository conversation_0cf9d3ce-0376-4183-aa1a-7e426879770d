<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Pinnacle Funding</title>

    <meta
      name="description"
      content="Pinnacle Funding offers fast flexible funding for small businesses. With our no-hassle application process, we will have you funded in under 24 hours."
    />
    <meta
      name="keywords"
      content="business funding, quick business loans, alternative lender, small business loans, payroll loans, fast funding, no hard credit check"
    />
    <meta name="author" content="Pinnacle Funding" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:title"
      content="Pinnacle Funding | Quick & Easy Business Funding"
    />
    <meta
      property="og:description"
      content="Pinnacle Funding offers fast flexible funding for small businesses. With our no-hassle application process, we will have you funded in under 24 hours."
    />
    <meta property="og:url" content="https://app.pinnaclefunding.com/" />
    <meta property="og:site_name" content="Pinnacle Funding" />
    <meta
      property="og:image"
      content="https://app.pinnaclefunding.com/assets/social-preview.jpg"
    />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Pinnacle Funding | Quick & Easy Business Funding"
    />
    <meta
      name="twitter:description"
      content="Fast, secure funding for small and mid-sized businesses. No hard credit check, apply online and get funded quickly."
    />
    <meta
      name="twitter:image"
      content="https://app.pinnaclefunding.com/assets/social-preview.jpg"
    />
    <meta name="twitter:site" content="@PinnacleFunding" />

    <link rel="preconnect" href="https://widget.trustpilot.com" crossorigin />

    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="MyWebSite" />
    <link rel="manifest" href="/site.webmanifest" />

    <!-- Critical CSS for above-the-fold content -->
    <style>
      @font-face {
        font-family: "Lexend";
        src: url("/src/assets/Lexend-VariableFont.ttf") format("truetype");
        font-weight: 100 900;
        font-style: normal;
        font-display: swap;
      }

      /* Critical Tailwind CSS - extracted for above-the-fold content */
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Lexend", ui-sans-serif, system-ui, sans-serif;
        line-height: 1.5;
        background-color: #f3f4f6;
        min-height: 100vh;
      }

      .prerender-container {
        min-height: 100vh;
        font-family: "Lexend", sans-serif;
        background-color: #f3f4f6;
      }

      /* Header styles */
      .header {
        background-color: white;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -4px rgba(0, 0, 0, 0.1);
      }

      .header-container {
        max-width: 80rem;
        margin: 0 auto;
        padding: 0 0.5rem;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        height: 4rem;
        align-items: center;
      }

      .logo-container {
        display: flex;
        align-items: center;
      }

      .logo {
        width: 7rem;
        max-height: 2.5rem;
        padding: 0.75rem 0;
        min-width: 0;
      }

      .phone-container {
        display: flex;
        align-items: center;
      }

      .phone-link {
        display: flex;
        align-items: center;
        font-weight: 500;
        color: #374151;
        text-decoration: none;
        font-size: 1rem;
        transition: color 0.3s;
      }

      .phone-link:hover {
        color: #2563eb;
      }

      .phone-icon {
        width: 1.25rem;
        height: 1.25rem;
        margin-right: 0.5rem;
        color: #4b5563;
      }

      /* Main content styles */
      .main-content {
        display: grid;
        padding: 2.5rem 0;
        min-height: calc(100vh - 4rem - 18.75rem);
      }

      .main-inner {
        align-self: center;
      }

      .page-container {
        max-width: 80rem;
        margin: 0 auto;
        padding: 0 1rem;
      }

      .content-grid {
        display: flex;
        flex-direction: column;
        padding: 1rem 0 2rem;
      }

      .explainer-section {
        width: 100%;
        margin-bottom: 1.5rem;
      }

      .form-section {
        width: 100%;
      }

      /* Explainer styles */
      .explainer-content {
        width: 100%;
        padding: 1.5rem;
      }

      .explainer-title {
        margin-bottom: 1.5rem;
      }

      .explainer-title h2 {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1e3a8a;
        margin-bottom: 0.75rem;
      }

      .promise-section {
        margin-bottom: 2.5rem;
      }

      .promise-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.75rem;
      }

      .promise-list {
        list-style: none;
      }

      .promise-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.75rem;
      }

      .promise-icon {
        margin-right: 0.5rem;
        color: #059669;
        margin-top: 0.125rem;
        width: 1.25rem;
        height: 1.25rem;
        flex-shrink: 0;
      }

      .promise-text {
        font-weight: 500;
        color: #1f2937;
        font-size: 0.875rem;
      }

      .promise-subtext {
        font-size: 0.75rem;
        color: #4b5563;
      }

      .trustpilot-widget {
        background-color: #f9fafb;
        height: 6.75rem;
        padding: 0.5rem;
        border-radius: 0.125rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
          0 1px 2px -1px rgba(0, 0, 0, 0.1);
        width: fit-content;
      }

      .trustpilot-placeholder {
        background-color: #e5e7eb;
        height: 6rem;
        padding: 0.5rem;
        border-radius: 0.125rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
          0 1px 2px -1px rgba(0, 0, 0, 0.1);
        width: fit-content;
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      .trustpilot-inner {
        width: 11rem;
        height: 5rem;
        background-color: #d1d5db;
        border-radius: 0.25rem;
      }

      /* Form styles */
      .form-container {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -4px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
      }

      .form-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .form-field {
        margin-bottom: 1rem;
      }

      .form-label {
        display: block;
        color: #374151;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      .form-input,
      .form-select {
        width: 100%;
        padding: 0.5rem 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.125rem;
        font-size: 1rem;
        transition: border-color 0.15s, box-shadow 0.15s;
      }

      .form-input:focus,
      .form-select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
      }

      .form-select {
        background-color: white;
        cursor: pointer;
      }

      /* Responsive styles */
      @media (min-width: 40rem) {
        .header-content {
          height: 5rem;
        }

        .logo {
          width: 10rem;
          max-height: 4rem;
        }

        .phone-link {
          font-size: 1.125rem;
        }

        .main-content {
          min-height: calc(100vh - 5rem - 21.75rem);
        }

        .explainer-title h2 {
          font-size: 1.5rem;
          margin-bottom: 1rem;
        }

        .promise-title {
          font-size: 1.25rem;
          margin-bottom: 1rem;
        }

        .promise-icon {
          width: 1.5rem;
          height: 1.5rem;
        }

        .promise-text {
          font-size: 1rem;
        }

        .promise-subtext {
          font-size: 0.875rem;
        }
      }

      @media (min-width: 64rem) {
        .header-container {
          padding: 0 2rem;
        }

        .header-content {
          height: 6rem;
        }

        .logo {
          width: 15rem;
        }

        .main-content {
          min-height: calc(100vh - 6rem - 23.25rem);
        }

        .content-grid {
          flex-direction: row;
          gap: 2rem;
          padding: 2rem 0;
        }

        .explainer-section {
          width: 33.3333%;
          margin-bottom: 0;
        }

        .form-section {
          width: 66.6667%;
        }
      }

      @keyframes pulse {
        50% {
          opacity: 0.5;
        }
      }

      /* Hide prerendered content when React loads */
      .react-loaded .prerender-content {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Prerendered above-the-fold content -->
    <div id="prerender-content" class="prerender-content prerender-container">
      <!-- Header -->
      <nav class="header">
        <div class="header-container">
          <div class="header-content">
            <div class="logo-container">
              <a href="https://pinnaclefundingco.com" class="logo-link">
                <img
                  src="src/assets/app-logo.svg"
                  alt="Pinnacle Funding"
                  class="logo"
                />
              </a>
            </div>
            <div class="phone-container">
              <a href="tel:3476948180" class="phone-link">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="phone-icon"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  />
                </svg>
                <span>+****************</span>
              </a>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="main-content">
        <div class="main-inner">
          <div class="page-container">
            <div class="content-grid">
              <!-- Explainer Section -->
              <div class="explainer-section">
                <div class="explainer-content">
                  <div class="explainer-title">
                    <h2>First step, let's figure out your funding.</h2>
                  </div>

                  <div class="promise-section">
                    <h3 class="promise-title">Our promise to you</h3>
                    <ul class="promise-list">
                      <li class="promise-item">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="promise-icon"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <div>
                          <p class="promise-text">We Guide, You Grow</p>
                          <p class="promise-subtext">
                            Your funding expert will be with you every step of
                            the way
                          </p>
                        </div>
                      </li>
                      <li class="promise-item">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="promise-icon"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <div>
                          <p class="promise-text">Zero Credit Impact</p>
                          <p class="promise-subtext">
                            We don't ever do a hard credit check
                          </p>
                        </div>
                      </li>
                      <li class="promise-item">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="promise-icon"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <div>
                          <p class="promise-text">Your Data is Protected</p>
                          <p class="promise-subtext">
                            We use 256 bit encryption to guard your data
                          </p>
                        </div>
                      </li>
                    </ul>
                  </div>

                  <!-- Trustpilot Widget Placeholder -->
                  <div class="trustpilot-placeholder">
                    <div class="trustpilot-inner"></div>
                  </div>
                </div>
              </div>

              <!-- Form Section -->
              <div class="form-section">
                <div class="form-container">
                  <h3 class="form-title">Let's Figure out your Funding</h3>

                  <div class="form-field">
                    <label for="fundingAmount" class="form-label">
                      How much funding do you need?
                    </label>
                    <input
                      type="text"
                      id="fundingAmount"
                      name="fundingAmount"
                      class="form-input"
                      placeholder="$50,000"
                    />
                  </div>

                  <div class="form-field">
                    <label for="purpose" class="form-label">
                      What will you use the funds for?
                    </label>
                    <select id="purpose" name="purpose" class="form-select">
                      <option value="">Select purpose...</option>
                      <option value="Expansion">Expansion</option>
                      <option value="WorkingCapital">Working capital</option>
                      <option value="Payroll">Payroll</option>
                      <option value="Equipment">Equipment</option>
                      <option value="BuyABusiness">Buy a business</option>
                      <option value="RealEstate">Real estate</option>
                      <option value="StartABusiness">Start a business</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>

                  <div class="form-field">
                    <label for="topPriority" class="form-label">
                      What's your Primary Funding Goal?
                    </label>
                    <select
                      id="topPriority"
                      name="topPriority"
                      class="form-select"
                    >
                      <option value="">Select primary funding goal...</option>
                      <option value="size">Size - Largest amount</option>
                      <option value="speed">Speed - Fastest approval</option>
                      <option value="cost">Cost - Lowest rate</option>
                    </select>
                  </div>

                  <div class="form-field">
                    <label for="timeline" class="form-label">
                      How soon are you looking for funding?
                    </label>
                    <select id="timeline" name="timeline" class="form-select">
                      <option value="">Select timeline...</option>
                      <option value="asap">
                        Immediately (Within 48 Hours)
                      </option>
                      <option value="week">Within a Week</option>
                      <option value="month">Within a Month</option>
                      <option value="longterm">Within the Next 6 Months</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script>
      // Hide prerendered content when React loads
      document.addEventListener("DOMContentLoaded", function () {
        const root = document.getElementById("root");
        const observer = new MutationObserver(function (mutations) {
          mutations.forEach(function (mutation) {
            if (mutation.type === "childList" && root.children.length > 0) {
              document.body.classList.add("react-loaded");
              observer.disconnect();
            }
          });
        });
        observer.observe(root, { childList: true });
      });
    </script>
  </body>
</html>
