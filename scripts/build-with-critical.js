#!/usr/bin/env node

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import critical from 'critical';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🚀 Building application with critical CSS extraction...\n');

// Step 1: Run the normal Vite build
console.log('📦 Building application...');
try {
  execSync('npm run build', { 
    stdio: 'inherit', 
    cwd: projectRoot 
  });
  console.log('✅ Build completed successfully\n');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 2: Extract critical CSS
console.log('🎨 Extracting critical CSS...');

const distDir = join(projectRoot, 'dist');
const indexHtmlPath = join(distDir, 'index.html');

if (!existsSync(indexHtmlPath)) {
  console.error('❌ index.html not found in dist directory');
  process.exit(1);
}

try {
  // Generate critical CSS
  const result = await critical.generate({
    base: distDir,
    src: 'index.html',
    target: {
      css: 'critical.css',
      html: 'index.html',
      uncritical: 'non-critical.css'
    },
    width: 1300,
    height: 900,
    dimensions: [
      {
        height: 500,
        width: 300,
      },
      {
        height: 900,
        width: 1200,
      },
      {
        height: 1080,
        width: 1920,
      }
    ],
    penthouse: {
      blockJSRequests: false,
      timeout: 30000,
      forceInclude: [
        // Force include critical Tailwind classes
        '.min-h-screen',
        '.font-lexend',
        '.bg-gray-100',
        '.text-',
        '.flex',
        '.grid',
        '.container',
        '.mx-auto',
        '.px-',
        '.py-',
        '.mb-',
        '.mt-',
        '.w-',
        '.h-',
        // Header styles
        '.header',
        '.logo',
        '.phone-',
        // Form styles that are above the fold
        '.form-',
        '.input-',
        '.button-',
        '.btn-',
        // Prerendered content styles
        '.prerender-',
        '.explainer-',
        '.promise-'
      ]
    },
    extract: true,
    inlineImages: false,
    assetPaths: [distDir],
    ignore: {
      atrule: ['@font-face'],
      rule: [/\.sr-only/],
      decl: (node, value) => {
        // Keep critical font declarations
        if (node.prop === 'font-family' && value.includes('Lexend')) {
          return false;
        }
        return false;
      }
    }
  });

  console.log('✅ Critical CSS extracted successfully');

  // Step 3: Optimize the HTML with better prerendered content
  const htmlContent = readFileSync(indexHtmlPath, 'utf8');
  
  // Inject additional performance optimizations
  const optimizedHtml = htmlContent
    .replace(
      '<head>',
      `<head>
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//www.google-analytics.com">
    <link rel="dns-prefetch" href="//widget.trustpilot.com">
    
    <!-- Preconnect to critical origins -->
    <link rel="preconnect" href="https://appsync.pinnaclefunding.com" crossorigin>
    
    <!-- Resource hints -->
    <link rel="prefetch" href="/src/assets/Lexend-VariableFont.ttf">
    
    <!-- Critical CSS will be inlined here by the critical package -->`
    )
    .replace(
      '</head>',
      `    <!-- Preload critical resources -->
    <link rel="modulepreload" href="/src/main.jsx">
    
    <!-- Non-critical CSS will be loaded asynchronously -->
    <script>
      // Load non-critical CSS asynchronously
      function loadCSS(href) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'print';
        link.onload = function() { this.media = 'all'; };
        document.head.appendChild(link);
      }
      
      // Load non-critical styles after page load
      window.addEventListener('load', function() {
        if (document.querySelector('link[href*="non-critical"]')) {
          loadCSS(document.querySelector('link[href*="non-critical"]').href);
        }
      });
    </script>
</head>`
    );

  writeFileSync(indexHtmlPath, optimizedHtml);
  
  console.log('✅ HTML optimized with performance enhancements');
  console.log('\n🎉 Build with critical CSS extraction completed successfully!');
  console.log('\n📊 Performance optimizations applied:');
  console.log('   • Critical CSS inlined');
  console.log('   • Non-critical CSS loaded asynchronously');
  console.log('   • DNS prefetch for external resources');
  console.log('   • Resource preloading');
  console.log('   • Font preloading');
  
} catch (error) {
  console.error('❌ Critical CSS extraction failed:', error.message);
  console.log('\n⚠️  Continuing with regular build (critical CSS extraction is optional)');
}
