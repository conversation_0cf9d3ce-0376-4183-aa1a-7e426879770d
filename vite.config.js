import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import UnpluginInjectPreload from "unplugin-inject-preload/vite";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    define: {
      "import.meta.env.CF_PAGES": env.CF_PAGES,
    },

    plugins: [
      react(),
      tailwindcss(),
      UnpluginInjectPreload({
        files: [
          {
            entryMatch: /Lexend-VariableFont.ttf$/,
          },
        ],
      }),
    ],
  };
});
