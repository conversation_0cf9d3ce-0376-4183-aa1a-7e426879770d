import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import UnpluginInjectPreload from "unplugin-inject-preload/vite";
import { createHtmlPlugin } from "vite-plugin-html";
// @ts-ignore - critical doesn't have types
import critical from "critical";

// Critical CSS extraction plugin
function criticalCSSPlugin() {
  return {
    name: "critical-css",
    apply: "build",
    writeBundle: {
      sequential: true,
      async handler(options, bundle) {
        const outputDir = options.dir || "dist";
        const htmlFiles = Object.keys(bundle).filter((file) =>
          file.endsWith(".html")
        );

        for (const htmlFile of htmlFiles) {
          try {
            console.log(`Extracting critical CSS for ${htmlFile}...`);

            await critical.generate({
              base: outputDir,
              src: htmlFile,
              target: {
                css: `critical-${htmlFile.replace(".html", "")}.css`,
                html: htmlFile,
                uncritical: `non-critical-${htmlFile.replace(".html", "")}.css`,
              },
              width: 1300,
              height: 900,
              dimensions: [
                {
                  height: 500,
                  width: 300,
                },
                {
                  height: 900,
                  width: 1200,
                },
              ],
              penthouse: {
                blockJSRequests: false,
                timeout: 30000,
              },
              extract: true,
              inlineImages: false,
              assetPaths: [outputDir],
              ignore: {
                atrule: ["@font-face"],
                rule: [/\.sr-only/],
                decl: (node, value) => {
                  // Keep critical font declarations
                  if (node.prop === "font-family" && value.includes("Lexend")) {
                    return false;
                  }
                  return false;
                },
              },
            });

            console.log(`✓ Critical CSS extracted for ${htmlFile}`);
          } catch (error) {
            console.warn(
              `Warning: Could not extract critical CSS for ${htmlFile}:`,
              error.message
            );
          }
        }
      },
    },
  };
}

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const isDev = mode === "development";

  return {
    define: {
      "import.meta.env.CF_PAGES": env.CF_PAGES,
    },

    plugins: [
      react(),
      tailwindcss(),
      UnpluginInjectPreload({
        files: [
          {
            entryMatch: /Lexend-VariableFont.ttf$/,
          },
        ],
      }),
      // Only run critical CSS extraction in production builds
      !isDev && criticalCSSPlugin(),
      createHtmlPlugin({
        minify: !isDev,
        inject: {
          data: {
            title: "Pinnacle Funding - Business Loans & Financing",
            description:
              "Get fast business funding with Pinnacle Funding. Quick approval, competitive rates, and flexible terms for your business needs.",
          },
        },
      }),
    ].filter(Boolean),

    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["react", "react-dom"],
            router: ["react-router-dom"],
            forms: ["react-hook-form"],
            ui: ["lucide-react"],
          },
        },
      },
      cssCodeSplit: true,
      sourcemap: false,
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },

    css: {
      devSourcemap: isDev,
    },
  };
});
