/**
 * Google Analytics utility functions
 */
import { logger } from "./logger";

/**
 * Initialize Google Analytics
 * @param {string} measurementId - Google Analytics measurement ID
 */
export function initializeGA(measurementId) {
  if (typeof window === "undefined") return;

  if (!measurementId) {
    logger.log("Google Analytics Measurement ID is not provided", "Analytics");
    return;
  }

  // Create script element for Google Analytics
  const script = document.createElement("script");
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
  document.head.appendChild(script);

  // Initialize the dataLayer and gtag function
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    window.dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", measurementId);

  // Make gtag available globally
  window.gtag = gtag;
}

const log = (...args) => {
  logger.log("[Analytics]: ", ...args);
};

/**
 * Track a click event in Google Analytics
 * @param {string} label - Event label (e.g., 'Logo', 'Phone', 'FAQ')
 * @param {Object} additionalParams - Additional event parameters
 */
export function trackClick(label, additionalParams = {}) {
  if (window.gtag) {
    log("Tracking click event:", label, additionalParams);
    window.gtag("event", "click", {
      event_label: label,
      ...additionalParams,
    });
  }
}

const skippedPersonalInfoFields = [
  "firstName",
  "lastName",
  "email",
  "phone",
  "ssn",
  "ein",

  "businessName",
  "dbaName",
  "website",
  "businessPhone",
  "businessEmail",

  "address.line1",
  "address.line2",

  "owners[0].firstName",
  "owners[0].lastName",
  "owners[0].email",
  "owners[0].phone",
  "owners[0].ssn",
  "owners[0].address.line1",
  "owners[0].address.line2",

  "owners[1].firstName",
  "owners[1].lastName",
  "owners[1].email",
  "owners[1].phone",
  "owners[1].ssn",
  "owners[1].address.line1",
  "owners[1].address.line2",
];

// Cache to store the last tracked value for each field
const fieldValueCache = {};

/**
 * Track a form field filled event in Google Analytics
 * @param {string} fieldName - Name of the field that was filled
 * @param {any} fieldValue - Value of the field (only included for specific fields)
 * @param {string} formName - Name of the form (e.g., 'PreQualification')
 */
export function trackFieldFilled(fieldName, fieldValue, formName) {
  if (window.gtag) {
    if (!fieldValue) return;

    if (fieldName == "businessStartMonth" || fieldName == "businessStartYear")
      return;

    // Create a unique cache key for this field and form
    const cacheKey = `${formName}:${fieldName}`;

    // Check if the value has changed using deep equality check
    if (
      fieldValueCache[cacheKey] !== undefined &&
      fieldValueCache[cacheKey] === fieldValue
    ) {
      // Value hasn't changed, don't track
      return;
    }

    fieldValueCache[cacheKey] = fieldValue;

    const eventParams = {
      event_label: fieldName,
      form_name: formName,
      field_filled: true,
    };

    if (fieldValue && !skippedPersonalInfoFields.includes(fieldName)) {
      eventParams.field_value = fieldValue;
    }

    if (
      fieldName == "owners[0].dateOfBirth" ||
      fieldName == "owners[1].dateOfBirth"
    ) {
      eventParams.field_value = fieldValue.split("-")[0];
    }

    log("Tracking form field filled event:", eventParams);
    window.gtag("event", "form_field_filled", eventParams);
  }
}

/**
 * Track form step completion in Google Analytics
 * @param {string} stepName - Name of the step that was completed
 * @param {string} formName - Name of the form (e.g., 'PreQualification')
 */
export function trackStepCompleted(stepName, formName) {
  if (window.gtag) {
    log("Tracking form step completed event:", stepName, formName);
    window.gtag("event", "form_step_completed", {
      event_label: stepName,
      form_name: formName,
    });
  }
}

/**
 * Track form submission in Google Analytics
 * @param {string} formName - Name of the form (e.g., 'PreQualification')
 * @param {Object} additionalParams - Additional event parameters
 */
export function trackFormSubmitted(formName, additionalParams = {}) {
  if (window.gtag) {
    log("Tracking form submitted event:", formName, additionalParams);
    window.gtag("event", "form_submitted", {
      event_label: formName,
      ...additionalParams,
    });
  }
}

export function trackOwnerAddedOrRemoved(added = true) {
  if (window.gtag) {
    log("Tracking owner added/removed event:", added ? "Added" : "Removed");
    window.gtag("event", added ? "owner_added" : "owner_removed", {
      event_label: `Second Owner ${added ? "Added" : "Removed"}`,
    });
  }
}

export function trackCustomEvent(eventName, value, once = true) {
  if (!window.gtag) return false;

  const valueStr =
    typeof value === "object" ? JSON.stringify(value) : String(value);
  const eventKey = `${eventName}:${valueStr}`;

  // Can only track once per session
  if (once) {
    const tracked = sessionStorage.getItem("trackedEvents") || "{}";
    const trackedEvents = JSON.parse(tracked);

    if (trackedEvents[eventKey]) {
      log("Skipping already tracked event:", eventName, { value });
      return false;
    }

    // Mark this event as tracked
    trackedEvents[eventKey] = true;
    sessionStorage.setItem("trackedEvents", JSON.stringify(trackedEvents));
  }
  // if value is an object, it should track the object (not wrapped in value)
  const params = value && typeof value === "object" ? value : { value };
  log("Tracking custom event:", eventName, params);
  window.gtag("event", eventName, params);
  return true;
}
