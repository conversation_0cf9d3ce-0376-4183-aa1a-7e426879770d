import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { SkeletonLoading } from "./SkeletonLoading";
import { ErrorState } from "./ErrorState";
import { PreQualifySuccess } from "./PreQualifySuccess";
import { PreQualifyDenied } from "./PreQualifyDenied";
import { useAppStorage } from "../../hooks/useAppStorage.js";
import { useGetAppApi } from "../../hooks/useGetAppApi.js";
import { APP_FLOW_STATUS } from "../../utils/consts.js";

/**
 * Pre-qualification result component that conditionally renders success or denied based on status
 * @returns {JSX.Element}
 */
export const PreQualifyResult = () => {
  const { uuid: appUuid } = useParams();
  const navigate = useNavigate();
  const { applicationId, setApplicationId, clearAllData, setPreQualifyResult } =
    useAppStorage();
  const [isInit, setInit] = useState(false);

  // Use our custom hook to fetch the application data
  const {
    isLoading,
    isSuccess,
    error,
    errorId,
    result: application,
  } = useGetAppApi(appUuid, true, null);

  if (applicationId !== appUuid) {
    clearAllData();
    setApplicationId(appUuid);
  }

  useEffect(() => {
    if (isSuccess && !isInit && application) {
      setPreQualifyResult(application);

      switch (application.status) {
        case APP_FLOW_STATUS.PREQUAL_DENIED:
          clearAllData();
          break;
        case APP_FLOW_STATUS.PREQUAL_FAST_TRACK:
        case APP_FLOW_STATUS.APP_STARTED:
        case APP_FLOW_STATUS.APP_COMPLETED:
        case APP_FLOW_STATUS.APP_SUBMITTED:
        case APP_FLOW_STATUS.APP_EDITING:
        case APP_FLOW_STATUS.APP_SIGNED:
          navigate(`/application/${appUuid}`);
          break;
        default:
          break;
      }

      setInit(true);
    }
  }, [
    appUuid,
    application,
    clearAllData,
    isInit,
    isSuccess,
    navigate,
    setPreQualifyResult,
  ]);

  if (error) {
    return <ErrorState message={error} errorId={errorId} />;
  }

  if (isLoading || !application) {
    return <SkeletonLoading />;
  }

  const renderContent = () => {
    let resultComponent;

    if (application.status === APP_FLOW_STATUS.PREQUAL_APPROVED) {
      resultComponent = <PreQualifySuccess application={application} />;
    } else if (application.status === APP_FLOW_STATUS.PREQUAL_DENIED) {
      resultComponent = <PreQualifyDenied application={application} />;
    } else {
      // Default to success for backward compatibility
      resultComponent = <PreQualifySuccess application={application} />;
    }

    return <div>{resultComponent}</div>;
  };

  return renderContent();
};
