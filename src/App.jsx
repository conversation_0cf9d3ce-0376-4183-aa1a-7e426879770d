import { useEffect, lazy, Suspense } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { PreQualifyResult } from "./components/PreQualifyResult/PreQualifyResult.jsx";
import { ApplicationResult } from "./components/ApplicationResult/ApplicationResult.jsx";
import { useQueryParamsLoad } from "./hooks/useQueryParamsLoad.js";
import { Header } from "./components/ui/Header.jsx";
import { PreQualifyPage } from "./pages/PreQualifyPage.jsx";
import { AppFormPage } from "./pages/AppFormPage.jsx";
import { NotFoundPage } from "./pages/NotFoundPage.jsx";
import { FastTrackPage } from "./pages/FastTrackPage.jsx";
import { initializeGA, trackCustomEvent } from "./utils/analytics.js";
import { initializeHotjar } from "./utils/hotjar.js";
import { initializeGTM } from "./utils/gtm.js";
import { IS_DEV_MODE } from "./utils/consts";
import { FooterSkeleton } from "./components/ui/Skeletons.jsx";

const Footer = lazy(() => import("./components/ui/Footer.jsx"));

const gaTrackingId = import.meta.env.VITE_GA_TRACKING_ID;
initializeGA(gaTrackingId);

initializeHotjar();
initializeGTM();

function trackUtmParameters(utmParams) {
  if (!utmParams || Object.keys(utmParams).length === 0) return;

  // Track each UTM parameter as a separate event
  Object.entries(utmParams).forEach(([key, value]) => {
    if (value) {
      trackCustomEvent(key, value);
    }
  });
}

/**
 * Main application component
 * @returns {JSX.Element}
 */
function App() {
  const { utmParams } = useQueryParamsLoad();

  if (IS_DEV_MODE) {
    console.log("Dev Mode!");
    console.table(import.meta.env);
  }

  // Track UTM parameters when the app loads
  useEffect(() => {
    if (utmParams && Object.keys(utmParams).length > 0) {
      trackUtmParameters(utmParams);
    }
  }, [utmParams]);

  return (
    <Router>
      <div className="min-h-screen font-lexend bg-gray-100">
        <Header />

        {IS_DEV_MODE && (
          <div className="px-10 py-2 w-full bg-red-200/50 text-center fixed z-10">
            <strong>Dev Mode!</strong>
          </div>
        )}

        <main className="grid py-10 min-h-[calc(100vh-64px-300px)] sm:min-h-[calc(100vh-80px-348px)] lg:min-h-[calc(100vh-96px-372px)]">
          <div className="self-center">
            <Routes>
              <Route path="/" element={<PreQualifyPage />} />
              <Route path="/ft" element={<FastTrackPage />} />
              <Route
                path="/prequalify-result/:uuid"
                element={<PreQualifyResult />}
              />
              <Route path="/application/:uuid" element={<AppFormPage />} />
              <Route
                path="/application/:uuid/result"
                element={<ApplicationResult />}
              />
              {/* Catch-all route for 404 page */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </div>
        </main>
        <Suspense fallback={<FooterSkeleton />}>
          <Footer />
        </Suspense>
      </div>
    </Router>
  );
}

export default App;
